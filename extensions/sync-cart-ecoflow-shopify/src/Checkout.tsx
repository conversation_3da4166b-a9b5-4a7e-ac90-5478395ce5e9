import {
  reactExtension,
  Banner,
  useInstructions,
  useTranslate,
  useCartLines,
  useSettings,
  useSessionToken,
  Button,
  useApplyCartLinesChange,
  Text,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useRef } from "react";

// 1. Choose an extension target
export default reactExtension("purchase.checkout.block.render", () => (
  <Extension />
));

function Extension() {
  const translate = useTranslate();
  const instructions = useInstructions();
  const cartLines = useCartLines();
  const cartLinesChange = useApplyCartLinesChange();
  const { get: getSessionToken } = useSessionToken();
  const { x_app_id, api_domain } = useSettings();
  const constantCartLines = useRef(cartLines);

  async function syncCartToEcoflowApi() {
    const token = await getSessionToken();
    const productList = cartLines.map((line) => ({
      productId: Number(
        line.merchandise.product.id.split("gid://shopify/Product/").pop()
      ),
      variantId: Number(
        line.merchandise.id.split("gid://shopify/ProductVariant/").pop()
      ),
      quantity: line.quantity,
      price: line.cost.totalAmount.amount,
      properties: line.attributes.reduce((acc, attribute) => {
        acc[attribute.key] = attribute.value;
        return acc;
      }, {}),
    }));
    
    await fetch(
      `${api_domain}/shopify/web/shopifyCart/mergeForShopifyCheckout`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-appid": x_app_id as string,
          "shopify-token": `Bearer ${token}`,
        },
        body: JSON.stringify({ productList }),
      }
    );
  }

  useEffect(() => {
    console.log("Syncing Cart to Ecoflow API", cartLines);
    console.log("Constant CartLines", constantCartLines.current);
    // Check if cart has changed by comparing current cart with previous cart
    const hasCartChanged =
      cartLines.length !== constantCartLines.current.length ||
      cartLines.some((cartLine) => {
        // Find matching line in constantCartLines
        const matchingLine = constantCartLines.current.find(
          (line) => line.id === cartLine.id
        );

        // If no matching line found, cart has changed
        if (!matchingLine) return true;

        // Check if merchandise ID, quantity or attributes have changed
        const isMerchandiseChanged =
          cartLine.merchandise.id !== matchingLine.merchandise.id;
        const isQuantityChanged = cartLine.quantity !== matchingLine.quantity;

        // Check if attributes have changed
        const areAttributesChanged =
          cartLine.attributes.length !== matchingLine.attributes.length ||
          cartLine.attributes.some((attr) => {
            const matchingAttr = matchingLine.attributes.find(
              (a) => a.key === attr.key
            );
            return !matchingAttr || matchingAttr.value !== attr.value;
          });

        return (
          isMerchandiseChanged || isQuantityChanged || areAttributesChanged
        );
      });

    constantCartLines.current = cartLines;

    if (hasCartChanged) {
      syncCartToEcoflowApi();
    }
  }, [cartLines]);

  // 2. Check instructions for feature availability, see https://shopify.dev/docs/api/checkout-ui-extensions/apis/cart-instructions for details
  if (!instructions.attributes.canUpdateAttributes) {
    // For checkouts such as draft order invoices, cart attributes may not be allowed
    // Consider rendering a fallback UI or nothing at all, if the feature is unavailable
    return (
      <Banner title="sync-cart-ecoflow-shopify" status="warning">
        {translate("attributeChangesAreNotSupported")}
      </Banner>
    );
  }

  return null;
//   return (
//     <>
//       <Text>For testing purposes only</Text>
//       <Button onPress={addLineItems}>Add Line Items</Button>
//       <Button onPress={removeLineItems}>Remove Line Items</Button>
//     </>
//   );

//   async function addLineItems() {
//     const firstLineItem = cartLines[0];
//     const result = await cartLinesChange({
//       type: "addCartLine",
//       merchandiseId: firstLineItem.merchandise.id,
//       quantity: 1,
//     });
//     console.log("Result", result);
//   }

//   async function removeLineItems() {
//     const lineItem = cartLines[0];
//     const result = await cartLinesChange({
//       type: "removeCartLine",
//       id: lineItem?.id,
//       quantity: lineItem?.quantity,
//     });
//     console.log("Result", result);
//   }
}
