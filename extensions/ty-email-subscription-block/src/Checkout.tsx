import {
  reactExtension,
  BlockStack,
  Checkbox,
  Text,
  Link,
  InlineLayout,
  Button,
  TextField,
  Heading,
  useEmail,
  useSettings,
  useTranslate,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useState } from "react";

// 1. Choose an extension target
export default reactExtension("purchase.thank-you.block.render", () => (
  <Extension />
));

function Extension() {
  console.log("the thank you email subscription block is rendered");
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");
  const [disclaimerChecked, setDisclaimerChecked] = useState(false);
  const [successSubscribe, setSuccessSubscribe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const customerEmail = useEmail();
  const translate = useTranslate();
  const {
    api_url,
    x_app_id,
    accepted_language,
    api_key,
    contact_list_id,
    single_field_id_1,
    single_field_value_1,
    single_field_overwrite_1,
    single_field_id_2,
    single_field_value_2,
    single_field_overwrite_2,
    multiple_field_id_1,
    multiple_field_value_1,
    multiple_field_overwrite_1,
    form_title,
    form_description,
    input_label,
    submit_button_text,
    privacy_policy_link,
    terms_of_service_link,
  } = useSettings();

  const createTraceId = () => {
    const date = new Date();
    const year = `00${date?.getFullYear()}`.slice(-2);
    const month = `00${date.getMonth() + 1}`.slice(-2);
    const day = `00${date.getDay()}`.slice(-2);
    const hour = `00${date.getHours()}`.slice(-2);
    const minute = `00${date.getMinutes()}`.slice(-2);
    const second = `00${date.getSeconds()}`.slice(-2);
    const millSecond = `000${date.getMilliseconds()}`.slice(-3);
    const randomStr1 = `${Math.random()}`;
    const randomStr2 = `${Math.random()}`;
    return (
      year +
      month +
      day +
      hour +
      minute +
      second +
      millSecond +
      randomStr1.slice(-8) +
      randomStr2.slice(-9)
    );
  };

  const validateEmail = (email: string) => {
    const regex =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return regex.test(String(email).toLowerCase());
  };

  const subscribeEmail = async (email: string) => {
    console.log("subscribeEmail", email);
    setEmailError("");
    if (!disclaimerChecked) {
      setEmailError(translate("disclaimerErrorMessage"));
      return;
    }
    if (!email) {
      setEmailError(translate("emptyEmailErrorMessage"));
      return;
    }
    if (!validateEmail(email)) {
      setEmailError(translate("invalidEmailErrorMessage"));
      return;
    }

    setIsLoading(true);
    const apiBody = {
      email,
      contactListId: contact_list_id as string,
      fields: [
        {
          id: single_field_id_1 as string,
          type: "single",
          value: Number(single_field_value_1),
          overwrite: single_field_overwrite_1 as boolean,
        },
        {
          id: single_field_id_2 as string,
          type: "single",
          value: Number(single_field_value_2),
          overwrite: single_field_overwrite_2 as boolean,
        },
        {
          id: multiple_field_id_1 as string,
          type: "multiple",
          value: (multiple_field_value_1 as string).split(",").map(Number),
          overwrite: multiple_field_overwrite_1 as boolean,
        },
      ],
    };


    try {
      const response = await fetch(api_url as string, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept-Language": accepted_language as string,
          "X-appid": x_app_id as string,
          apiKey: api_key as string,
          traceId: createTraceId(),
        },
        body: JSON.stringify(apiBody),
      });
      const data = await response.json();
      if (data.message === "Success") {
        setSuccessSubscribe(true);
      } else {
        setEmailError(translate("generalFailedMessage"));
        setSuccessSubscribe(false);
      }

      console.log("data", data);
    } catch (error) {
      console.error("Error subscribing email", error);
      setEmailError(translate("generalFailedMessage"));
      setSuccessSubscribe(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log("customerEmail", customerEmail);
    if (customerEmail) {
      setEmail(customerEmail);
    }
  }, []);

  // 3. Render a UI
  return (
    <BlockStack border="base" padding="loose" borderRadius="base">
      <Heading level={1}>{form_title ? form_title : translate("formTitle")}</Heading>
      <Text appearance="subdued">
        {form_description ? form_description : translate("formDescription")}
      </Text>
      <InlineLayout columns={["fill", "25%"]} spacing="base">
        <TextField
          label={input_label ? input_label as string : translate("inputLabel")}
          value={email}
          onChange={(value) => setEmail(value)}
          type="email"
          disabled={isLoading || successSubscribe}
        />
        <Button
          onPress={() => subscribeEmail(email)}
          disabled={isLoading || successSubscribe}
          loading={isLoading}
        >
          {submit_button_text ? submit_button_text : translate("submitButtonText")}
        </Button>
      </InlineLayout>
      {emailError && <Text appearance="critical">{emailError}</Text>}
      {successSubscribe ? (
        <Text appearance="accent">
          {translate("successMessage")}
        </Text>
      ) : (
        <Checkbox
          id="checkbox"
          name="checkbox"
          onChange={(value) => setDisclaimerChecked(value)}
          checked={disclaimerChecked}
        >
          <Text>
            {translate("disclaimer", {
              privacyPolicyLink: (
                <Link to={privacy_policy_link as string} external>
                  {translate("privacyPolicyText")}
                </Link>
              ),
              termsOfServiceLink: (
                <Link to={terms_of_service_link as string} external>
                  {translate("termsOfServiceText")}
                </Link>
              ),
            })}
          </Text>
        </Checkbox>
      )}
    </BlockStack>
  );
}
