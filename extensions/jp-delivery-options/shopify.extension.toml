# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2024-10"

[[extensions]]
name = "jp-delivery-options"
handle = "jp-delivery-options"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.checkout.block.render"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
# network_access = true

# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]
[[extensions.settings.fields]]
key = "widget_title"
type = "single_line_text_field"
name = "Widget title"
description = "Title for the banner (Larger font size)"
[[extensions.settings.fields]]
key = "widget_subtitle"
type = "single_line_text_field"
name = "Widget Subtitle"
description = "Subtitle for the banner (Smaller font size)"
[[extensions.settings.fields]]
key = "first_option_label"
type = "single_line_text_field"
name = "First option text label"
description = "Text label for the first option"
[[extensions.settings.fields]]
key = "first_option_price_label"
type = "single_line_text_field"
name = "First option price label"
description = "Price label for the first option. E.g. 無料"
[[extensions.settings.fields]]
key = "second_option_label"
type = "single_line_text_field"
name = "Second option text label"
description = "Text label for the second option"
[[extensions.settings.fields]]
key = "second_option_price_label"
type = "single_line_text_field"
name = "Second option price label"
description = "Price label for the second option. E.g. 無料"
