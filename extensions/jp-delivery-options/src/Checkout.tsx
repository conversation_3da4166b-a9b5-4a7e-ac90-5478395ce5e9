import {
  reactExtension,
  Banner,
  BlockStack,
  Checkbox,
  Text,
  useApi,
  useApplyAttributeChange,
  useInstructions,
  useTranslate,
  ToggleButtonGroup,
  BlockLayout,
  ToggleButton,
  View,
  InlineLayout,
  useAttributes,
  InlineStack,
  HeadingGroup,
  Heading,
  useCartLines,
  useSettings,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useState } from "react";

// 1. Choose an extension target
export default reactExtension("purchase.checkout.block.render", () => (
  <Extension />
));

function Extension() {
  const translate = useTranslate();
  const instructions = useInstructions();
  const attributes = useAttributes();
  const cartLines = useCartLines();
  const applyAttributeChange = useApplyAttributeChange();
  const [selectedOption, setSelectedOption] = useState("separate");
  const [showWidget, setShowWidget] = useState(false);
  const {
    widget_title: widgetTitle,
    widget_subtitle: widgetSubtitle,
    first_option_label: firstOptionLabel,
    first_option_price_label: firstOptionPriceLabel,
    second_option_label: secondOptionLabel,
    second_option_price_label: secondOptionPriceLabel,
  } = useSettings();

  useEffect(() => {
    if (
      cartLines.length === 1 &&
      cartLines[0].merchandise?.title.includes("+")
    ) {
      setShowWidget(true);
    } else if (cartLines.length > 1) {
      setShowWidget(true);
    }
  }, [cartLines]);

  // 2. Check instructions for feature availability, see https://shopify.dev/docs/api/checkout-ui-extensions/apis/cart-instructions for details
  if (!instructions.attributes.canUpdateAttributes) {
    // For checkouts such as draft order invoices, cart attributes may not be allowed
    // Consider rendering a fallback UI or nothing at all, if the feature is unavailable
    return (
      <Banner title="jp-delivery-options" status="warning">
        {translate("attributeChangesAreNotSupported")}
      </Banner>
    );
  }

  // 3. Render a UI
  return (
    <>
      {showWidget ? (
        <ToggleButtonGroup
          value={selectedOption}
          onChange={(value) => {
            console.log(`onChange event with value: ${value}`);
            setSelectedOption(value);
            onCheckboxChange(value);
          }}
        >
          <BlockStack>
            <InlineStack blockAlignment="end" spacing="none">
              <Heading level={1} inlineAlignment="start">
                {widgetTitle}
              </Heading>
              <Heading level={2} inlineAlignment="start">
                {widgetSubtitle}
              </Heading>
            </InlineStack>
            <ToggleButton id="separate">
              <InlineLayout columns={["fill", "auto"]}>
                <Text>{firstOptionLabel}</Text>
                <Text emphasis="bold">{firstOptionPriceLabel}</Text>
              </InlineLayout>
            </ToggleButton>
            <ToggleButton id="group">
              <InlineLayout columns={["fill", "auto"]}>
                <Text>{secondOptionLabel}</Text>
                <Text emphasis="bold">{secondOptionPriceLabel}</Text>
              </InlineLayout>
            </ToggleButton>
          </BlockStack>
        </ToggleButtonGroup>
      ) : null}
    </>
  );

  async function onCheckboxChange(value) {
    await applyAttributeChange({
      key: "DeliveryOption",
      type: "updateAttribute",
      value,
    });
  }
}
