import {
  Banner,
  useApi,
  useTranslate,
  reactExtension,
  Text,
  useSettings,
  useSubtotalAmount,
  useSubscription,
  useCustomer,
  useAppMetafields,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useRef, useState } from "react";

export default reactExtension("purchase.checkout.block.render", () => (
  <Extension />
));

type SettingProps = {
  enable_tier_1: boolean;
  price_off_tier_1: number;
  price_from_tier_1: number;
  price_to_tier_1: number;
  enable_tier_2: boolean;
  price_off_tier_2: number;
  price_from_tier_2: number;
  price_to_tier_2: number;
  enable_tier_3: boolean;
  price_off_tier_3: number;
  price_from_tier_3: number;
  price_to_tier_3: number;
  enable_tier_4: boolean;
  price_off_tier_4: number;
  price_from_tier_4: number;
  price_to_tier_4: number;
  enable_tier_5: boolean;
  price_off_tier_5: number;
  price_from_tier_5: number;
  price_to_tier_5: number;
};

// const dummyTier = {
//   enable_tier_1: true,
//   price_off_tier_1: 0,
//   price_from_tier_1: 0,
//   price_to_tier_1: 600,
//   enable_tier_2: true,
//   price_off_tier_2: 40,
//   price_from_tier_2: 600,
//   price_to_tier_2: 1500,
//   enable_tier_3: true,
//   price_off_tier_3: 120,
//   price_from_tier_3: 1500,
//   price_to_tier_3: 2300,
//   enable_tier_4: true,
//   price_off_tier_4: 200,
//   price_from_tier_4: 2300,
//   price_to_tier_4: 3000,
//   enable_tier_5: true,
//   price_off_tier_5: 300,
//   price_from_tier_5: 3000,
//   price_to_tier_5: 1000000,
// };

function Extension() {
  const translate = useTranslate();
  const {
    i18n,
    cost: { subtotalAmount },
    lines,
    discountAllocations,
  } = useApi();
  const customer = useCustomer();
  const metafields = useAppMetafields({
    type: "variant",
    namespace: "custom",
    key: "applicable_for_promotion",
  });
  const {
    enable_tier_1,
    price_off_tier_1,
    price_from_tier_1,
    price_to_tier_1,
    enable_tier_2,
    price_off_tier_2,
    price_from_tier_2,
    price_to_tier_2,
    enable_tier_3,
    price_off_tier_3,
    price_from_tier_3,
    price_to_tier_3,
    enable_tier_4,
    price_off_tier_4,
    price_from_tier_4,
    price_to_tier_4,
    enable_tier_5,
    price_off_tier_5,
    price_from_tier_5,
    price_to_tier_5,
  } = useSettings<SettingProps>();

  // const {
  //   enable_tier_1,
  //   price_off_tier_1,
  //   price_from_tier_1,
  //   price_to_tier_1,
  //   enable_tier_2,
  //   price_off_tier_2,
  //   price_from_tier_2,
  //   price_to_tier_2,
  //   enable_tier_3,
  //   price_off_tier_3,
  //   price_from_tier_3,
  //   price_to_tier_3,
  //   enable_tier_4,
  //   price_off_tier_4,
  //   price_from_tier_4,
  //   price_to_tier_4,
  //   enable_tier_5,
  //   price_off_tier_5,
  //   price_from_tier_5,
  //   price_to_tier_5,
  // } = dummyTier;

  const [price, setPrice] = useState<
    | {
        priceFrom: number;
        priceTo: number;
        priceOff: number;
        priceOffForMessage: number;
      }
    | undefined
  >();
  const [tier, setTier] = useState<number | undefined>();
  const [subTotal, setSubTotal] = useState<number>(0);

  const getPriceFromTier = (tier: number) => {
    switch (tier) {
      case 1:
        return {
          priceFrom: price_from_tier_1,
          priceTo: price_to_tier_1,
          priceOff: price_off_tier_1,
          priceOffForMessage: price_off_tier_2,
        };
      case 2:
        return {
          priceFrom: price_from_tier_2,
          priceTo: price_to_tier_2,
          priceOff: price_off_tier_2,
          priceOffForMessage: price_off_tier_3,
        };
      case 3:
        return {
          priceFrom: price_from_tier_3,
          priceTo: price_to_tier_3,
          priceOff: price_off_tier_3,
          priceOffForMessage: price_off_tier_4,
        };
      case 4:
        return {
          priceFrom: price_from_tier_4,
          priceTo: price_to_tier_4,
          priceOff: price_off_tier_4,
          priceOffForMessage: price_off_tier_5,
        };
      case 5:
        return {
          priceFrom: price_from_tier_5,
          priceTo: price_to_tier_5,
          priceOff: price_off_tier_5,
          priceOffForMessage: price_off_tier_5,
        };
      default:
        return { priceFrom: 0, priceTo: 0, priceOff: 0, priceOffForMessage: 0 };
    }
  };

  const getTier = (total: number) => {
    if (enable_tier_1 && total <= price_to_tier_1) {
      return 1;
    } else if (enable_tier_2 && total <= price_to_tier_2) {
      return 2;
    } else if (enable_tier_3 && total <= price_to_tier_3) {
      return 3;
    } else if (enable_tier_4 && total <= price_to_tier_4) {
      return 4;
    } else if (enable_tier_5 && total <= price_to_tier_5) {
      return 5;
    } else {
      return 0;
    }
  };

  const getTierPrice = (amount: number) => {
    const tier = getTier(amount);
    const priceData = getPriceFromTier(tier);
    return priceData;
  };

  // const tier = getTier();
  console.log("Selected discountCodes:", discountAllocations.current);
  console.log("Selected discountCodes:", lines.current);
  // console.log("metafields:", metafields);
  const metafieldsRef = useRef(metafields);
  // console.log(metafieldsRef.current, "metafieldsRef.current");

  useEffect(() => {
    if (
      metafields.length > 0 &&
      metafieldsRef.current.length !== metafields.length
    ) {
      metafieldsRef.current = metafields;
    }
  }, [metafields]);

  useEffect(() => {
    // Get target variant ids from metafields
    const targetVariantIds = metafieldsRef.current
      .filter(
        (metafield) =>
          metafield.metafield.key === "applicable_for_promotion" &&
          metafield.metafield.value === "true"
      )
      .map((metafield) => metafield.target.id);
    // Get matching line items from target variant ids
    const matchingLineItems = lines.current.filter((line) =>
      targetVariantIds.includes(line.merchandise.id.split("/").pop())
    );
    console.log(matchingLineItems, "matchingLineItems");
    // Get discount code from discount allocation array
    const discountCodes = matchingLineItems.flatMap(
      (line) =>
        line.discountAllocations?.filter(
          (allocation) => allocation.type === "code"
        ) || []
    );
    // Calculate total discount amount from discount code
    const totalDiscountedAmount = discountCodes.reduce(
      (total, code) => total + code.discountedAmount.amount,
      0
    );

    // Get custom discount from tier discount script injected in script editor
    const customDiscount = matchingLineItems.flatMap(
      (line) =>
        line.discountAllocations?.filter(
          (allocation) =>
            allocation.type === "custom" &&
            // There are 2 custom discount in script editor
            // Exclude the discount which message title includes "off for you"
            !allocation.title.includes("off for you")
        ) || []
    );
    // Calculate total discount amount from custom discount
    const totalTierDiscountedAmount = customDiscount.reduce(
      (total, code) => total + code.discountedAmount.amount,
      0
    );
    // const priceObject = getTierPrice(subtotalAmount.current.amount);
    console.log(totalTierDiscountedAmount, "total discount amount: tier");
    console.log(totalDiscountedAmount, "total discount amount: code");
    // No tier discount should be counted when customer is not logged in
    const priceOff = customer ? totalTierDiscountedAmount : 0;
    const promotionProductsSubtotal = matchingLineItems.reduce(
      (total, line) => total + line.cost.totalAmount.amount,
      0
    );
    // Calculate total price with discounted amount (original price)
    const priceWithOutDiscount =
      promotionProductsSubtotal + priceOff + totalDiscountedAmount;
    console.log(priceWithOutDiscount, "priceWithOutDiscount");
    setSubTotal(priceWithOutDiscount);
    const tierBeforeDiscount = getTierPrice(priceWithOutDiscount);
    const originalTier = getTier(priceWithOutDiscount);
    setPrice(tierBeforeDiscount);
    setTier(originalTier);
    console.log(tierBeforeDiscount, "tierBeforeDiscount");
  }, [metafieldsRef.current]);

  // console.log(price, "price");
  // console.log(tier);

  const DiscountBanner = () => {
    const isMinTier = tier === 1;
    const isMaxTier = tier === 5;

    if (!customer && !isMinTier) {
      return translate("loginMessage", {
        priceOff: (
          <Text emphasis="bold">
            {i18n.formatCurrency(Number(price.priceOff), {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })}
          </Text>
        ),
        priceFrom: (
          <Text emphasis="bold">
            {i18n.formatCurrency(Number(price.priceFrom), {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })}
          </Text>
        ),
      });
    } else if (!customer && isMinTier) {
      return translate("minTierLoginMessage");
    } else if (customer && !isMaxTier) {
      return translate("tierDiscountMessage", {
        priceDifference: (
          <Text emphasis="bold">
            {i18n.formatCurrency(Number(price.priceTo) - subTotal, {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })}
          </Text>
        ),
        priceOff: (
          <Text emphasis="bold">
            {i18n.formatCurrency(Number(price.priceOffForMessage), {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })}
          </Text>
        ),
        priceTo: (
          <Text emphasis="bold">
            {i18n.formatCurrency(Number(price.priceTo), {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })}
          </Text>
        ),
      });
    } else if (customer && isMaxTier) {
      return translate("maxTierDiscountMessage", {
        priceOff: (
          <Text emphasis="bold">
            {i18n.formatCurrency(Number(price.priceOffForMessage), {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })}
          </Text>
        ),
        priceFrom: (
          <Text emphasis="bold">
            {i18n.formatCurrency(Number(price.priceFrom), {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })}
          </Text>
        ),
      });
    }
  };

  return (
    <>
      {price ? (
        <Banner status="success">
          <Text appearance="success">
            <DiscountBanner />
          </Text>
        </Banner>
      ) : null}
    </>
  );
}
