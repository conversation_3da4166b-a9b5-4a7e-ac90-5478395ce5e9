# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2024-04"

[[extensions]]
name = "tier-discount-message-banner"
handle = "tier-discount-message-banner"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.checkout.block.render"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
# network_access = true

# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

[[extensions.metafields]]
namespace = "custom"
key = "applicable_for_promotion"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]
[[extensions.settings.fields]]
key = "enable_tier_1"
type = "boolean"
name = "Enable Tier 1"
[[extensions.settings.fields]]
key = "price_off_tier_1"
type = "number_integer"
name = "Tier1: Price Off"
[[extensions.settings.fields]]
key = "price_from_tier_1"
type = "number_integer"
name = "Tier1: Price from"
[[extensions.settings.fields]]
key = "price_to_tier_1"
type = "number_integer"
name = "Tier1: Price to"
[[extensions.settings.fields]]
key = "enable_tier_2"
type = "boolean"
name = "Enable Tier 2"
[[extensions.settings.fields]]
key = "price_off_tier_2"
type = "number_integer"
name = "Tier2: Price Off"
[[extensions.settings.fields]]
key = "price_from_tier_2"
type = "number_integer"
name = "Tier2: Price from"
[[extensions.settings.fields]]
key = "price_to_tier_2"
type = "number_integer"
name = "Tier2: Price to"
[[extensions.settings.fields]]
key = "enable_tier_3"
type = "boolean"
name = "Enable Tier 3"
[[extensions.settings.fields]]
key = "price_off_tier_3"
type = "number_integer"
name = "Tier3: Price Off"
[[extensions.settings.fields]]
key = "price_from_tier_3"
type = "number_integer"
name = "Tier3: Price from"
[[extensions.settings.fields]]
key = "price_to_tier_3"
type = "number_integer"
name = "Tier3: Price to"
[[extensions.settings.fields]]
key = "enable_tier_4"
type = "boolean"
name = "Enable Tier 4"
[[extensions.settings.fields]]
key = "price_off_tier_4"
type = "number_integer"
name = "Tier4: Price Off"
[[extensions.settings.fields]]
key = "price_from_tier_4"
type = "number_integer"
name = "Tier4: Price from"
[[extensions.settings.fields]]
key = "price_to_tier_4"
type = "number_integer"
name = "Tier4: Price to"
[[extensions.settings.fields]]
key = "enable_tier_5"
type = "boolean"
name = "Enable Tier 5"
[[extensions.settings.fields]]
key = "price_off_tier_5"
type = "number_integer"
name = "Tier5: Price Off"
[[extensions.settings.fields]]
key = "price_from_tier_5"
type = "number_integer"
name = "Tier5: Price from"
[[extensions.settings.fields]]
key = "price_to_tier_5"
type = "number_integer"
name = "Tier5: Price to"