import {
  useApi,
  useTranslate,
  reactExtension,
  useApplyNoteChange,
  Checkbox,
  TextField,
  BlockStack,
} from "@shopify/ui-extensions-react/checkout";
import React = require("react");

export default reactExtension(
  "purchase.checkout.reductions.render-after",
  () => <Extension />
);

function Extension() {
  const t = useTranslate();
  const [checked, setChecked] = React.useState<boolean>(true);
  const applyNoteChange = useApplyNoteChange();

  // Used in onChange, trigger this function when the input is done or blur
  const onNoteChangeEnd = (note: string) => {
    applyNoteChange({
      type: "updateNote",
      note,
    });
  };

  // Used in onInput, trigger this function in every input change
  const onNoteChange = (note: string) => {
    // If the note is empty, remove it
    if (!note) {
      applyNoteChange({
        type: "removeNote",
      });
    }
  };

  // Remove the note if the checkbox is unchecked
  React.useEffect(() => {
    if (!checked) {
      applyNoteChange({
        type: "removeNote",
      });
    }
  }, [checked]);

  return (
    <BlockStack>
      <Checkbox
        id="notes-checkbox"
        name="notes-checkbox"
        checked={checked}
        onChange={() => setChecked((prevs) => !prevs)}
      >
        {t("notes.notes")}
      </Checkbox>
      {checked ? (
        <TextField
          label={t("notes.inputPlaceholder")}
          id="notes-text-field"
          name="notes-text-field"
          multiline
          onChange={onNoteChangeEnd}
          onInput={onNoteChange}
        />
      ) : null}
    </BlockStack>
  );
}
