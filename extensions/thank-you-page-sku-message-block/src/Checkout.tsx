import {
  reactExtension,
  Banner,
  useInstructions,
  useTranslate,
  useCartLines,
  useSettings,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useState } from "react";

// 1. Choose an extension target
export default reactExtension("purchase.thank-you.block.render", () => (
  <Extension />
));

function Extension() {
  const translate = useTranslate();
  const instructions = useInstructions();
  const cartLines = useCartLines();
  const [showBanner, setShowBanner] = useState<boolean>(false);
  const { variant_id: variantId, banner_title: bannerTitle, banner_content: bannerContent } = useSettings();

  useEffect(() => {
    console.log("This is the cart lines in thank you ui extension", cartLines);
    cartLines.some((lineItem) => {
      if (lineItem.merchandise.id.split("gid://shopify/ProductVariant/").pop() === variantId) {
        console.log("This is the variant id in thank you ui extension", variantId);
        setShowBanner(true);
      }
    });
  }, [cartLines]);


  // 2. Check instructions for feature availability, see https://shopify.dev/docs/api/checkout-ui-extensions/apis/cart-instructions for details
  if (!instructions.attributes.canUpdateAttributes) {
    // For checkouts such as draft order invoices, cart attributes may not be allowed
    // Consider rendering a fallback UI or nothing at all, if the feature is unavailable
    return (
      <Banner title="thank-you-ui-ext" status="warning">
        {translate("attributeChangesAreNotSupported")}
      </Banner>
    );
  }

  // 3. Render a UI
  return (
    showBanner ? (
      <Banner title={bannerTitle as string} status="success">
        {bannerContent as string}
      </Banner>
    ) : (
      <></>
    )
  );
}