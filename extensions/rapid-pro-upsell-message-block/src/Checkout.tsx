import {
  reactExtension,
  Banner,
  Image,
  Text,
  useInstructions,
  useTranslate,
  InlineLayout,
  useCartLines,
  useSettings,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useState } from "react";

// 1. Choose an extension target
export default reactExtension(
  "purchase.checkout.cart-line-list.render-after",
  () => <Extension />
);

interface MessageBlockSettingsProps {
  wn_product_product_ids: string;
  smart_device_product_product_ids: string;
  rapid_pro_product_product_ids: string;
  three_c_product_product_ids: string;
  wn_promotion_message_text: string;
  smart_device_promotion_message_text: string;
  wn_smart_device_promotion_message_text: string;
  wn_smart_device_rapid_pro_promotion_message_text: string;
  three_c_promotion_message_text: string;
  rapid_pro_promotion_message_image_url: string;
  three_c_promotion_message_image_url: string;
  [key: string]: string | undefined;
}

function Extension() {
  const translate = useTranslate();
  const instructions = useInstructions();
  const cartLines = useCartLines();
  const {
    wn_product_product_ids: wnProductIds,
    smart_device_product_product_ids: smartDeviceProductIds,
    rapid_pro_product_product_ids: rapidProProductIds,
    three_c_product_product_ids: threeCProductIds,
    wn_promotion_message_text: wnPromotionMessageText,
    smart_device_promotion_message_text: smartDevicePromotionMessageText,
    wn_smart_device_promotion_message_text: wnSmartDevicePromotionMessageText,
    wn_smart_device_rapid_pro_promotion_message_text: wnSmartDeviceRapidProPromotionMessageText,
    three_c_promotion_message_text: threeCPromotionMessageText,
    rapid_pro_promotion_message_image_url: rapidProPromotionMessageImageUrl,
    three_c_promotion_message_image_url: threeCPromotionMessageImageUrl,
  } = useSettings<MessageBlockSettingsProps>();
  const [showRapidProBanner, setShowRapidProBanner] = useState<boolean>(false);
  const [showShow3CBanner, setShowShow3CBanner] = useState<boolean>(false);
  const [rapidProUpsellMessageText, setRapidProUpsellMessageText] =
    useState<string>("");
  const [accessoriesUpsellMessageText, setAccessoriesUpsellMessageText] =
    useState<string>("");

  // const wnProductIds =
  //   "9944636784983,**************,**************,*************,*************,*************,*************,*************,*************";
  // const smartDeviceProductIds = "**************,**************,**************";
  // const rapidProProductIds = "**************,**************";
  // const threeCProductIds =
  //   "**************,**************,**************,**************,**************,**************,**************,*************";
  // const wnSmartDeviceRapidProPromotionMessageText =
  //   "You've added a power bank! Now, get up to 25% off power bank accessories!";
  // const wnPromotionMessageText =
  //   "You've added a Smart Device! Here's 20% off the RAPID Pro Series.";
  // const smartDevicePromotionMessageText =
  //   "You've added a STREAM Series product! Here's 20% off the RAPID Pro Series.";
  // const wnSmartDevicePromotionMessageText =
  //   "You've added a STREAM Series product! Here's 20% off the RAPID Pro Series.";
  // const threeCPromotionMessageText =
  //   "You've added a power bank! Now, get up to 25% off accessories!";

  useEffect(() => {
    const isWnProductInCart = cartLines.some((line) =>
      wnProductIds?.includes(
        line.merchandise.product.id.split("gid://shopify/Product/").pop()
      )
    );
    const isSmartDeviceProductInCart = cartLines.some((line) =>
      smartDeviceProductIds?.includes(
        line.merchandise.product.id.split("gid://shopify/Product/").pop()
      )
    );
    const isRapidProProductInCart = cartLines.some((line) =>
      rapidProProductIds?.includes(
        line.merchandise.product.id.split("gid://shopify/Product/").pop()
      )
    );
    const isThreeCProductInCart = cartLines.some((line) =>
      threeCProductIds?.includes(
        line.merchandise.product.id.split("gid://shopify/Product/").pop()
      )
    );

    // Only WN product in cart and no rapid pro products in cart
    if (
      isWnProductInCart &&
      !isSmartDeviceProductInCart &&
      !isRapidProProductInCart
    ) {
      setShowRapidProBanner(true);
      setShowShow3CBanner(false);
      setRapidProUpsellMessageText(wnPromotionMessageText);
    }

    // Only Smart Device product in cart and no rapid pro products in cart
    if (
      !isWnProductInCart &&
      isSmartDeviceProductInCart &&
      !isRapidProProductInCart
    ) {
      setShowRapidProBanner(true);
      setShowShow3CBanner(false);
      setRapidProUpsellMessageText(smartDevicePromotionMessageText);
    }

    // WN and Smart Device product in cart and no rapid pro products in cart
    if (
      isWnProductInCart &&
      isSmartDeviceProductInCart &&
      !isRapidProProductInCart
    ) {
      setShowRapidProBanner(true);
      setShowShow3CBanner(false);
      setRapidProUpsellMessageText(wnSmartDevicePromotionMessageText);
    }

    // WN or Smart Device product in cart and rapid pro product in cart
    if (
      (isWnProductInCart || isSmartDeviceProductInCart) &&
      isRapidProProductInCart
    ) {
      setShowRapidProBanner(false);
      setShowShow3CBanner(true);
      setAccessoriesUpsellMessageText(
        wnSmartDeviceRapidProPromotionMessageText
      );
    }

    // Only 3C product in cart and no WN or Smart Device product in cart
    if (
      isThreeCProductInCart &&
      !isWnProductInCart &&
      !isSmartDeviceProductInCart
    ) {
      setShowRapidProBanner(false);
      setShowShow3CBanner(true);
      setAccessoriesUpsellMessageText(threeCPromotionMessageText);
    }
  }, [cartLines]);

  // 2. Check instructions for feature availability, see https://shopify.dev/docs/api/checkout-ui-extensions/apis/cart-instructions for details
  if (!instructions.attributes.canUpdateAttributes) {
    // For checkouts such as draft order invoices, cart attributes may not be allowed
    // Consider rendering a fallback UI or nothing at all, if the feature is unavailable
    return (
      <Banner title="rapid-pro-upsell-message-block" status="warning">
        {translate("attributeChangesAreNotSupported")}
      </Banner>
    );
  }

  // 3. Render a UI
  return (
    <>
      {showRapidProBanner && (
        <Banner status="success">
          <InlineLayout
            columns={["12%", "fill"]}
            inlineAlignment="center"
            blockAlignment="center"
            spacing="tight"
            borderRadius="base"
          >
            <Image source={rapidProPromotionMessageImageUrl} />
            <Text>{rapidProUpsellMessageText}</Text>
          </InlineLayout>
        </Banner>
      )}
      {showShow3CBanner && (
        <Banner status="success">
          <InlineLayout
            columns={["12%", "fill"]}
            inlineAlignment="center"
            blockAlignment="center"
            spacing="tight"
            background="subdued"
            border="base"
            padding="tight"
            borderRadius="base"
          >
            <Image source={threeCPromotionMessageImageUrl} />
            <Text>{accessoriesUpsellMessageText}</Text>
          </InlineLayout>
        </Banner>
      )}
    </>
  );
}
