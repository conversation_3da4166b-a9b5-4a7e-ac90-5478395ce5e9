import {
  reactExtension,
  Banner,
  useInstructions,
  useTranslate,
  useShippingAddress,
  useBuyerJourneyIntercept,
  useSettings,
  useCartLines,
} from "@shopify/ui-extensions-react/checkout";
import ZIP from "./Zip";

// 1. Choose an extension target
export default reactExtension("purchase.checkout.block.render", () => (
  <Extension />
));

function Extension() {
  const shippingAddress = useShippingAddress();
  const cartLines = useCartLines();
  const {
    store_name: storeName,
    validate_variant: validateVariant,
    error_message: errorMessage,
  } = useSettings();
  const zipCodes = ZIP[storeName as keyof typeof ZIP];
  console.log(cartLines, "cartLines");
  const validateVariantIds = validateVariant?.toString().split(",") || [];
  const isValidateVariant = cartLines.some((line) => {
    const variantId = line.merchandise.id.split("/").pop();
    return validateVariantIds.includes(variantId);
  });

  console.log(isValidateVariant, "isValidateVariant");

  useBuyerJourneyIntercept(({ canBlockProgress }) => {
    console.log(
      canBlockProgress && !zipCodes.includes(shippingAddress.zip),
      "canBlockProgress"
    );
    const isInvalid = isValidateVariant && !zipCodes.includes(shippingAddress.zip);
    return canBlockProgress && isInvalid
      ? {
          behavior: "block", 
          reason: "Invalid shipping zip code",
          errors: [
            {
              message: errorMessage as string,
              target: "$.cart.deliveryGroups[0].deliveryAddress.zip",
            },
          ],
        }
      : {
          behavior: "allow",
        };
  });

  // 3. Render a UI
  return null;
}
