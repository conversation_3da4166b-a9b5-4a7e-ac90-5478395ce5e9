/**
 * This extension is for showing a banner to indicate if the zip code is valid for installation service
 * Zip code is validated by the backend api
 * There are 5 cases of validation:
 * 1. Installation product is in the cart, and the zip code is valid
 * 2. Installation product is in the cart, and the zip code is invalid
 * 3. Non-installation product is in the cart, and the zip code is valid
 * 4. Non-installation product is in the cart, and the zip code is invalid
 * 5. No installation related product in the cart
 * 
 * For case 1, a success banner will be shown, but content is not the same as case 3
 * For case 2, a critical banner will be shown and the checkout will be blocked
 * For case 3, a success banner will be shown, but content is not the same as case 1
 * For case 4, a critical banner will be shown and the checkout WILL NOT be blocked
 * For case 5, no banner will be shown
 */
import {
  reactExtension,
  Banner,
  BlockStack,
  Text,
  useShippingAddress,
  useInstructions,
  useTranslate,
  Button,
  useCartLines,
  Link,
  useSettings,
  SkeletonTextBlock,
  useBuyerJourneyIntercept,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useMemo, useState } from "react";

// 1. Choose an extension target
export default reactExtension("purchase.checkout.delivery-address.render-after", () => (
  <Extension />
));

// Dummy data for testing, keep it for testing purpose
// const ZIPS = ["55234", "78652", "78653", "78654", "78655", "78656", "78657"];
// const INSTALLATION_IDS = [
//   4094401744841, 4169498391545, 4169492027089, 4169495035113, 4185531924873,
// ];
// const NON_INSTALLATION_IDS = [
//   4079030406561, 4079030475689, 4075883010649, 4196323685785, 4076788360837,
//   4076788640905, 4179332549841, 4196327066361, 4196327070129, 4196327073897,
//   4192538869673, 4172959413505, 47749970493735,
// ];
// const dummyCartLines = [
//   {
//     id: "123",
//     quantity: 1,
//     merchandise: {
//       id: "gid://shopify/ProductVariant/47749970493735",
//       product: {
//         id: "123",
//       },
//     },
//   },
// ];

function Extension() {
  const translate = useTranslate();
  const instructions = useInstructions();
  const address = useShippingAddress();
  const cartLines = useCartLines();
  const {
    api_url,
    app_id,
    installation_product_variant_ids,
    non_installation_product_variant_ids,
  } = useSettings();
  const [isZipValid, setIsZipValid] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isApiCalled, setIsApiCalled] = useState(false);
  const [shouldBlockCheckout, setShouldBlockCheckout] = useState(false);

  // Convert the variant ids to array for both installation and non-installation products
  const installationProductVariantIds = String(installation_product_variant_ids)
    .split(",")
    .map((id) => id.trim());
  const nonInstallationProductVariantIds = String(
    non_installation_product_variant_ids
  )
    .split(",")
    .map((id) => id.trim());

  // Check if there is any installation product in the cart
  const isInstallationProduct = cartLines.some((line) => {
    const variantId = String(line.merchandise.id.split("/").pop());
    return installationProductVariantIds.includes(variantId);
  });

  // Check if there is any non-installation product in the cart
  const isNonInstallationProduct = cartLines.some((line) => {
    const variantId = String(line.merchandise.id.split("/").pop());
    return nonInstallationProductVariantIds.includes(variantId);
  });

  // Determine the priority of the product
  const productPriority = () => {
    // if there is both installation and non-installation product in the cart, return installation
    if (isInstallationProduct && isNonInstallationProduct) {
      return "installation";
    }
    if (isInstallationProduct) {
      return "installation";
    }
    if (isNonInstallationProduct) {
      return "nonInstallation";
    }
    // if there is no product in the cart, return none
    if (!isInstallationProduct && !isNonInstallationProduct) {
      return "none";
    }
  };

  console.log(
    isInstallationProduct,
    isNonInstallationProduct,
    cartLines,
    "this is the cart lines"
  );

  // 2. Check instructions for feature availability, see https://shopify.dev/docs/api/checkout-ui-extensions/apis/cart-instructions for details
  if (!instructions.attributes.canUpdateAttributes) {
    // For checkouts such as draft order invoices, cart attributes may not be allowed
    // Consider rendering a fallback UI or nothing at all, if the feature is unavailable
    return (
      <Banner title="dpu-zip-validation-banner" status="warning">
        {translate("attributeChangesAreNotSupported")}
      </Banner>
    );
  }

  const fetchZipCode = async (zip: string) => {
    try {
      setLoading(true);
      const response = await fetch(
        `${api_url}/website/electricianScope/shopify/checkZipCode?zipCode=${zip}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "x-appid": String(app_id),
          },
        }
      );
      const data = await response.json();
      setIsZipValid(data.data);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
      setIsApiCalled(true);
    }
  };

  useEffect(() => {
    if (address.zip) {
      // check if the zip is in the list of valid zip codes
      fetchZipCode(address.zip);
      // setIsZipValid(ZIPS.includes(address.zip));
    } else {
      setIsApiCalled(false);
    }
  }, [address.zip]);


  const InstallationErrorBanner = useMemo(() => {
    return (
      <Banner status="critical">
        <BlockStack inlineAlignment="start">
          <Text>{translate("installationUnavailableMessage")}</Text>
          <Text>{translate("supportInfoMessage")}</Text>
          <Button to="https://www.ecoflow.com/us/ef-quote-form">
            {translate("getAFreeQuoteButton")}
          </Button>
        </BlockStack>
      </Banner>
    );
  }, []);

  // TODO: Remove it if client confirm that they don't need this banner
  // No use for now, keep it here for just in case client wants to show a success banner
  const InstallationSuccessBanner = useMemo(() => {
    return (
      <Banner status="success">
        <BlockStack inlineAlignment="start">
          <Text>{translate("installationSuccessMessage")}</Text>
          {/* <Button to="https://www.ecoflow.com/us/ef-quote-form">
            {translate("getAFreeQuoteButton")}
          </Button> */}
        </BlockStack>
      </Banner>
    );
  }, []);

  const NonInstallationErrorBanner = useMemo(() => {
    return (
      <Banner status="info">
        <BlockStack inlineAlignment="start">
          <Text>{translate("nonInstallationErrorMessage")}</Text>
          <Text>
            {translate("nonInstallationSupportInfoMessage", {
              email: (
                <Link to="mailto:<EMAIL>"><EMAIL></Link>
              ),
            })}
          </Text>
        </BlockStack>
      </Banner>
    );
  }, []);

  const NonInstallationSuccessBanner = useMemo(() => {
    return (
      <Banner status="success">
        <BlockStack inlineAlignment="start">
          <Text>
            {translate("nonInstallationSuccessMessage", {
              email: (
                <Link to="mailto:<EMAIL>"><EMAIL></Link>
              ),
            })}
          </Text>
          <Text>{translate("nonInstallationSuccessInfoMessage")}</Text>
        </BlockStack>
      </Banner>
    );
  }, []);

  // Show the status banner based on the product priority and the zip code validation
  const StatusBanner = () => {
    if (loading) {
      return <SkeletonTextBlock />;
    }
    // Case 2
    if (productPriority() === "installation" && !isZipValid) {
      setShouldBlockCheckout(true);
      return InstallationErrorBanner;
    }
    // Case 1
    if (productPriority() === "installation" && isZipValid) {
      setShouldBlockCheckout(false);
      return InstallationSuccessBanner;
    }
    // Case 4
    if (productPriority() === "nonInstallation" && !isZipValid) {
      setShouldBlockCheckout(false);
      return NonInstallationErrorBanner;
    }
    // Case 3
    if (productPriority() === "nonInstallation" && isZipValid) {
      setShouldBlockCheckout(false);
      return NonInstallationSuccessBanner;
    }
    // Case 5
    if (productPriority() === "none") {
      setShouldBlockCheckout(false);
      return null;
    }
  };

  useBuyerJourneyIntercept(
    ({canBlockProgress}) => {
      return canBlockProgress &&
        shouldBlockCheckout
        ? {
            behavior: 'block',
            reason: 'Installation service is not available in your area'
          }
        : {
            behavior: 'allow',
          };
    },
  );

  // 3. Render a UI
  return (
    <>
      {isApiCalled && <StatusBanner />}
    </>
  );
}
