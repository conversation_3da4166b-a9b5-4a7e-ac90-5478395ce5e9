# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-01"

[[extensions]]
name = "installation-service-validation-block"
handle = "installation-service-validate"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.checkout.delivery-address.render-after"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true
block_progress = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
network_access = true

# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]
[[extensions.settings.fields]]
key = "api_url"
type = "single_line_text_field"
name = "API URL"
description = "Enter the API URL"
[[extensions.settings.fields]]
key = "app_id"
type = "single_line_text_field"
name = "App ID"
description = "Enter the app ID"
[[extensions.settings.fields]]
key = "non_bundle_installation_ids"
type = "single_line_text_field"
name = "Non Bundle Installation ProductIDs"
description = "Enter the variant ids for non bundle installation products, separated by commas without spaces. E.g. 1,2,3,4"
[[extensions.settings.fields]]
key = "installation_ids_array"
type = "multi_line_text_field"
name = "Installation IDs Array"
description = "Enter the installation IDs array in format [[Installation Service Variant ID 1, Installation Service Variant ID 2], [Pair Product Variant ID 1, Pair Product Variant ID 2]] and then line break for each pair"
[[extensions.settings.fields]]
key = "installation_product_missing_message"
type = "single_line_text_field"
name = "Installation Product Missing Message"
description = "Enter the installation product missing message"
[[extensions.settings.fields]]
key = "zip_code_valid_message"
type = "single_line_text_field"
name = "Zip Code Valid Message"
description = "Enter the zip code valid message"
[[extensions.settings.fields]]
key = "zip_code_invalid_message"
type = "single_line_text_field"
name = "Zip Code Invalid Message"
description = "Enter the zip code invalid message"
