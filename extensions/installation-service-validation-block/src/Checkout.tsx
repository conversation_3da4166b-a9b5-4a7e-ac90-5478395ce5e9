import {
  reactExtension,
  Banner,
  useSettings,
  useCartLines,
  useShippingAddress,
  useBuyerJourneyIntercept,
  SkeletonTextBlock,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useState } from "react";

// 1. Choose an extension target
export default reactExtension(
  "purchase.checkout.delivery-address.render-after",
  () => <Extension />
);

function Extension() {
  console.log("installation-service-validation-block is rendered");
  const address = useShippingAddress();
  const {
    non_bundle_installation_ids,
    installation_ids_array,
    api_url,
    app_id,
    installation_product_missing_message,
    zip_code_valid_message,
    zip_code_invalid_message,
  } = useSettings();
  const cartLines = useCartLines();
  const [
    isNonBundleInstallationProductsIncluded,
    setIsNonBundleInstallationProductsIncluded,
  ] = useState(false);
  const [isInstallationProductsIncluded, setIsInstallationProductsIncluded] =
    useState(false);
  const [isInstallationInvalid, setIsInstallationInvalid] = useState(false);
  const [isZipValid, setIsZipValid] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isApiCalled, setIsApiCalled] = useState(false);
  const [shouldBlockCheckout, setShouldBlockCheckout] = useState(false);

  console.log("installation_ids_array", installation_ids_array);
  const installationIdsArray = installation_ids_array
    ? String(installation_ids_array)
        .split("\n")
        .filter((line) => line.trim() !== "")
        .map((line) => JSON.parse(line.trim()))
    : [];

  console.log("installationIdsArray", installationIdsArray);
  console.log("cartLines", cartLines);

  const fetchZipCode = async (zip: string) => {
    try {
      setLoading(true);
      const response = await fetch(
        `${api_url}/website/electricianScope/shopify/checkZipCode?zipCode=${zip}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "x-appid": String(app_id),
          },
        }
      );
      const data = await response.json();
      setIsZipValid(data.data);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
      setIsApiCalled(true);
    }
  };

  useEffect(() => {
    if (address.zip) {
      // check if the zip is in the list of valid zip codes
      fetchZipCode(address.zip);
      // setIsZipValid(ZIPS.includes(address.zip));
    } else {
      setIsApiCalled(false);
    }
  }, [address.zip]);

  useEffect(() => {
    const nonBundleInstallationIds = non_bundle_installation_ids as string;
    const nonBundleInstallationIdsArray = nonBundleInstallationIds?.split(",") || [];
    const nonBundleInstallationProductsIncluded = cartLines.some((line) =>
      nonBundleInstallationIdsArray.includes(
        line.merchandise.id.split("gid://shopify/ProductVariant/").pop()
      )
    );
    setIsNonBundleInstallationProductsIncluded(
      nonBundleInstallationProductsIncluded
    );
    const installationProductsIncluded = installationIdsArray.filter((id) =>
      cartLines.some((line) =>
        id[0].includes(
          Number(
            line.merchandise.id.split("gid://shopify/ProductVariant/").pop()
          )
        )
      )
    );
    setIsInstallationProductsIncluded(installationProductsIncluded.length > 0);
    console.log("installationProductsIncluded", installationProductsIncluded);
    if (installationProductsIncluded.length === 0) {
      setIsInstallationInvalid(false);
      setShouldBlockCheckout(false);
      return;
    }
    const matchPairs = installationProductsIncluded.filter(
      (installationItem) => {
        const [_, secondArray] = installationItem;

        const isCartLineIncluded = cartLines.some((cartLine) =>
          secondArray.includes(
            Number(
              cartLine.merchandise.id
                .split("gid://shopify/ProductVariant/")
                .pop()
            )
          )
        );
        console.log("isCartLineIncluded", isCartLineIncluded);
        return isCartLineIncluded;
      }
    );
    console.log("matchPairs", matchPairs);
    if (matchPairs.length < installationProductsIncluded.length) {
      setIsInstallationInvalid(true);
      setShouldBlockCheckout(true);
    } else if (
      matchPairs.length > 0 &&
      matchPairs.length === installationProductsIncluded.length
    ) {
      setIsInstallationInvalid(false);
      setShouldBlockCheckout(false);
    }
  }, [cartLines]);

  const StatusBanner = () => {
    if (loading) {
      return <SkeletonTextBlock />;
    }
    if (isInstallationInvalid) {
      return (
        <Banner status="critical">
          {installation_product_missing_message}
        </Banner>
      );
    }
    if (isApiCalled) {
      if (
        (!isZipValid && isInstallationProductsIncluded) ||
        (!isZipValid && isNonBundleInstallationProductsIncluded)
      ) {
        setShouldBlockCheckout(true);
        return <Banner status="critical">{zip_code_invalid_message}</Banner>;
      }
      if (
        (isZipValid &&
        !isInstallationInvalid &&
        isInstallationProductsIncluded) ||
        (isZipValid &&
          !isInstallationInvalid &&
          isNonBundleInstallationProductsIncluded)
      ) {
        setShouldBlockCheckout(false);
        return <Banner status="success">{zip_code_valid_message}</Banner>;
      }
    }
    return null;
  };

  useBuyerJourneyIntercept(({ canBlockProgress }) => {
    console.log("shouldBlockCheckout", canBlockProgress, shouldBlockCheckout);
    return canBlockProgress && shouldBlockCheckout
      ? {
          behavior: "block",
          reason: "Installation service is not available in your area",
        }
      : {
          behavior: "allow",
        };
  });

  // 3. Render a UI
  return <StatusBanner />;
}
