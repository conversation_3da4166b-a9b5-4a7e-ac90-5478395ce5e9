import { CartLine } from "@shopify/ui-extensions/checkout";

// Promo config interface used in the extension logic
export type BasePromoConfig = {
  id: string;
  type: "gwp" | "bxgy";
  enabled: boolean;
  product_page_url: string;
  bannerTitle: string;
  successMessage: string;
  pendingMessage: string;
};

export type GWPConfig = BasePromoConfig & {
  type: "gwp";
  productId: string;
  threshold: number;
  label: string;
  inventoryLimit: number;
};
export type BXGYConfig = BasePromoConfig & {
  type: "bxgy";
  productXId: string;
  productXExcludedVariantIds: string;
  productYId: string;
};

// Union type for all promotion configs
export type PromoConfig = GWPConfig | BXGYConfig;

// Settings interface that maps to configuration values defined in the extension's TOML file
export type PromoSettings = Partial<{
  // GWP1 settings
  enable_free_sample_product: boolean;
  free_product_id: string;
  free_prod_limit: string;
  free_prod_tag_1: string;
  free_prod_inventory_limit: string;
  gwp1_url: string;

  // GWP2 settings
  enable_free_sample_product_2: boolean;
  free_product_id_2: string;
  free_prod_limit_2: string;
  free_prod_tag_2: string;
  free_prod_inventory_limit_2: string;
  gwp2_url: string;

  // BXGY settings
  enable_bxgy: boolean;
  product_x_id: string;
  product_x_excluded_variant_ids: string;
  product_y_id: string;
  bxgy_url: string;
}>;

export type PromotionLines = {
  gwp: CartLine | null;
  bxgy: {
    x: CartLine | null;
    y: CartLine | null;
  };
};
