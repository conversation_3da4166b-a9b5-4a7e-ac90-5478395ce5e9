# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-01"

[[extensions]]
name = "gwp-checkout"
handle = "gwp-checkout"
type = "ui_extension"

# Controls where in Shopify your extension will be injected,
# and the file that contains your extension's source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.checkout.block.render"

[extensions.capabilities]
# Gives your extension access to directly query Shopify's storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
network_access = true



# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]
[[extensions.settings.fields]]
key = "enable_free_sample_product"
type = "boolean"
name = "Enable GWP 1"
description = "Enable first gift with purchase"

[[extensions.settings.fields]]
key = "free_product_id"
type = "single_line_text_field"
name = "GWP 1 Variant ID"
description = "Variant ID for first gift"

[[extensions.settings.fields]]
key = "free_prod_limit"
type = "number_decimal"
name = "GWP 1 Threshold"
description = "Cart amount needed for first gift"

[[extensions.settings.fields]]
key = "free_prod_inventory_limit"
type = "number_integer"
name = "GWP 1 Inventory Limit"
description = "Disable GWP 1 when inventory falls below this number"

[[extensions.settings.fields]]
key = "free_prod_tag_1"
type = "single_line_text_field"
name = "GWP 1 Label"
description = "Label for first gift banner"

[[extensions.settings.fields]]
key = "gwp1_url"
type = "single_line_text_field"
name = "GWP1 URL"
description = "URL for the first gift product page"

# GWP 2 Settings
[[extensions.settings.fields]]
key = "enable_free_sample_product_2"
type = "boolean"
name = "Enable GWP 2"
description = "Enable second gift with purchase"

[[extensions.settings.fields]]
key = "free_product_id_2"
type = "single_line_text_field"
name = "GWP 2 Variant ID"
description = "Variant ID for second gift"

[[extensions.settings.fields]]
key = "free_prod_limit_2"
type = "number_decimal"
name = "GWP 2 Threshold"
description = "Cart amount needed for second gift"

[[extensions.settings.fields]]
key = "free_prod_inventory_limit_2"
type = "number_integer"
name = "GWP 2 Inventory Limit"
description = "Disable GWP 2 when inventory falls below this number"

[[extensions.settings.fields]]
key = "free_prod_tag_2"
type = "single_line_text_field"
name = "GWP 2 Label"
description = "Label for second gift banner"

[[extensions.settings.fields]]
key = "gwp2_url"
type = "single_line_text_field"
name = "GWP2 URL"
description = "URL for the second gift product page"

# Buy X Get Y Settings - BXGY-DISABLED: Temporarily disabled, uncomment to re-enable
# [[extensions.settings.fields]]
# key = "enable_bxgy"
# type = "boolean"
# name = "Enable Buy X Get Y"
# description = "Enable Buy X Get Y promotion"
#
# [[extensions.settings.fields]]
# key = "product_x_id"
# type = "single_line_text_field"
# name = "Product X (Product ID)"
# description = "Product ID of the qualifying product"
#
# [[extensions.settings.fields]]
# key = "product_x_excluded_variant_ids"
# type = "multi_line_text_field"
# name = "Product X (excluded: Variant IDs)"
# description = "Variant IDs to exclude from the promotion. Separate IDs with commas. Example: 1234567890,1234567891"
#
# [[extensions.settings.fields]]
# key = "product_y_id"
# type = "single_line_text_field"
# name = "Product Y (Variant ID)"
# description = "Variant ID of the free product"
#
# [[extensions.settings.fields]]
# key = "bxgy_url"
# type = "single_line_text_field"
# name = "Buy X Get Y (Product X URL Page)"
# description = "URL for the BXGY product page"
