/**
 * GWP Checkout V5 (Checkout UI Extension) - Version 1.0.0
 *
 * Implements a promotional system in the checkout page that handles:
 * - Gift with Purchase (GWP) offers
 * - Buy X Get Y (BXGY) promotions
 *
 * Key features include:
 * - Multiple tier GWP support (GWP1 and GWP2) -> add OR remove GWP based on cart total; GWP is always 1 quantity
 * - BXGY support
 * - Promotions are configured via the Checkout UI Extension settings
 * - Automatic promotion handling based on cart total (use USD as the currency to check promotions eligibility)
 * - Implemented Currency Conversion for promotions eligibility check
 * - Display of claimed and unclaimed promotions
 *
 * @see README.md for version history
 */

import { useState, useEffect, useRef } from "react";
import {
  reactExtension,
  useCartLines,
  useSettings,
  useSubtotalAmount,
  useApplyCartLinesChange,
  useApi,
  useTotalAmount,
} from "@shopify/ui-extensions-react/checkout";
import { View } from "@shopify/ui-extensions-react/checkout";
import { CartLine } from "@shopify/ui-extensions/checkout";
import LoadingBanner from "./components/LoadingBanner";
import ClaimedPromotions from "./components/ClaimedPromotions";
import UnclaimedPromotions from "./components/UnclaimedPromotions";

// Helpers & Types
import { CurrencyConversion } from "../helpers/currency";
import {
  createPromoConfig,
  findPromotionLine,
  handlePromotion,
} from "../helpers/promotions";
import { calculateCartTotal } from "../helpers/cart";
import { CurrencyConversionResponse } from "../types/currency";

export default reactExtension("purchase.checkout.block.render", () => (
  <Extension />
));

// TODO: inventory limit check is not yet implemented

// TODO: The following settings are for testing; Remove or Comment out before deployment
// const settings = {
//   // GWP1 settings
//   enable_free_sample_product: false,
//   free_product_id: "43839911821452",
//   free_prod_limit: "75.00",
//   free_prod_tag_1: "Free Gift 1",
//   free_prod_inventory_limit: "5",
//   gwp1_url: "",

//   // GWP2 settings
//   enable_free_sample_product_2: false,
//   free_product_id_2: "43839912575116",
//   free_prod_limit_2: "150.00",
//   free_prod_tag_2: "Free Gift 2",
//   free_prod_inventory_limit_2: "5",
//   gwp2_url:
//     "",

//   // BXGY settings
//   enable_bxgy: true,
//   product_x_id: "8181160575116",
//   product_x_excluded_variant_ids: "44190101700748, 44190103732364",
//   product_y_id: "43866151092364",
//   bxgy_url:
//     "",
// };

type PromoState = {
  gwp1Added: boolean;
  gwp2Added: boolean;
  bxgyAdded: boolean;
};

function Extension() {
  // TODO: remove render count logs before deployment
  const renderCount = useRef(0);
  console.log("GWP_CT", ++renderCount.current);

  const { localization, query } = useApi();
  const settings = useSettings();
  const cartLines: CartLine[] = useCartLines() || [];
  const subtotalAmount = useSubtotalAmount();
  const totalAmount = useTotalAmount();
  const applyCartLinesChange = useApplyCartLinesChange();

  const [isCurrencyReady, setIsCurrencyReady] = useState<boolean>(false);
  const [currencyConversion, setCurrencyConversion] =
    useState<CurrencyConversion | null>(null);
  const [promoHandlingAttempts, setPromoHandlingAttempts] = useState(0);

  const isProcessing = useRef(false);
  const hasReRun = useRef(false);
  const promoStateRef = useRef<PromoState>({
    gwp1Added: false,
    gwp2Added: false,
    bxgyAdded: false,
  });

  const promotionConfigs = createPromoConfig(settings);

  const fetchCurrencyRate = async (
    country: string
  ): Promise<CurrencyConversion | null> => {
    try {
      const response: CurrencyConversionResponse = await query(
        `query @inContext(country: ${country}) {
          metaobject(handle: {
            type: "currency",
            handle: "currency-conversion"
          }) {
            field(key: "conversion") {
              value
            }   
          }
        }`
      );

      if (response.errors) {
        console.error("Error fetching currency rate:", response.errors);
        return;
      }

      const data = response.data;
      const conversionValue: string = data?.metaobject?.field?.value;
      if (!conversionValue) return;

      return JSON.parse(conversionValue);
    } catch (error) {
      console.error("Error fetching currency rate:", error);
    }
  };

  const handlePromotions = async () => {
    if (isProcessing.current) {
      // IP = isProcessing
      console.log(
        `🚨 GWP_CT ${renderCount.current} IP: ${isProcessing.current}`
      );
      hasReRun.current = true;
    }

    if (!isCurrencyReady || !cartLines || isProcessing.current) return;

    const cartTotal = calculateCartTotal({
      cartLines,
      currencyConversion,
    });

    try {
      isProcessing.current = true;

      console.log(`GWP_CT ${renderCount.current} CART TOTAL: ${cartTotal}`);
      console.log(
        `GWP_CT ${renderCount.current} ATTEMPTS: ${promoHandlingAttempts}`
      );

      for (const [key, config] of Object.entries(promotionConfigs)) {
        const promotionLine = findPromotionLine(cartLines, config);

        await handlePromotion({
          config,
          cartTotal,
          promotionLine,
          promoState: promoStateRef.current,
          setPromoState: (newState: PromoState) => {
            promoStateRef.current = newState;
          },
          applyCartLinesChange,
        });
      }
    } catch (error) {
      console.error("Error processing promotions:", error);
    } finally {
      isProcessing.current = false;

      if (hasReRun.current) {
        hasReRun.current = false;
        setPromoHandlingAttempts(promoHandlingAttempts + 1);
      }

      // TODO: remove this log after PROD deployment testing
      // IFP = Finally isProcessing
      console.log(
        `🚨 GWP_CT ${renderCount.current} FIP: ${isProcessing.current}`
      );
    }
  };

  useEffect(() => {
    handlePromotions();
  }, [isCurrencyReady, cartLines, promoHandlingAttempts, totalAmount]);

  useEffect(() => {
    const fetchAndSetup = async () => {
      if (subtotalAmount?.currencyCode === "JPY") {
        setIsCurrencyReady(true);
        return;
      }

      const countryCode = localization?.country?.current?.isoCode;
      const rates = await fetchCurrencyRate(countryCode);
      if (rates) {
        setCurrencyConversion(rates);
        setIsCurrencyReady(true);
      }
    };

    fetchAndSetup();
  }, [subtotalAmount?.currencyCode]);

  return (
    <View>
      {!isCurrencyReady ? (
        <LoadingBanner />
      ) : (
        <>
          <ClaimedPromotions
            cartLines={cartLines}
            promoConfigs={promotionConfigs}
          />

          <UnclaimedPromotions
            cartLines={cartLines}
            promoConfigs={promotionConfigs}
            currencyConversion={currencyConversion}
          />
        </>
      )}
    </View>
  );
}
