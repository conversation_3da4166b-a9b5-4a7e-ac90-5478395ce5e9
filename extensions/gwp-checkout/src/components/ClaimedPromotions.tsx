import { Banner, Text, BlockStack } from "@shopify/ui-extensions-react/checkout";
import { CartLine } from "@shopify/ui-extensions/checkout";
import { findPromotionLine } from "../../helpers/promotions";
import { GWPConfig, PromoConfig, PromotionLines } from "../../types/promotion";

type Props = {
  cartLines: CartLine[];
  promoConfigs: {
    [key: string]: PromoConfig;
  };
};

const ClaimedPromotions = ({ cartLines, promoConfigs }: Props) => {
  const { gwp1, gwp2, bxgy } = promoConfigs;

  const gwp1Added = findPromotionLine(cartLines, promoConfigs.gwp1) as PromotionLines["gwp"];
  const gwp2Added = findPromotionLine(cartLines, promoConfigs.gwp2) as PromotionLines["gwp"];
  // BXGY-DISABLED: Code preserved for future use
  const bxgyAdded = findPromotionLine(cartLines, promoConfigs.bxgy) as PromotionLines["bxgy"];

  return (
    <BlockStack spacing="tight" padding={["base", "none", "none", "none"]}>
      {gwp1Added && gwp1.enabled && (
        <Banner title={(gwp1 as GWPConfig).label|| promoConfigs.gwp1.bannerTitle} status="success">
          <Text>{promoConfigs.gwp1.successMessage}</Text>
        </Banner>
      )}

      {gwp2Added && gwp2.enabled && (
        <Banner title={(gwp2 as GWPConfig).label || promoConfigs.gwp2.bannerTitle} status="success">
          <Text>{promoConfigs.gwp2.successMessage}</Text>
        </Banner>
      )}

      {/* BXGY-DISABLED: Banner component temporarily disabled but preserved for future use */}
      {/*
      {bxgyAdded.y && bxgy.enabled && (
        <Banner title={promoConfigs.bxgy.bannerTitle} status="success">
          <Text>{promoConfigs.bxgy.successMessage}</Text>
        </Banner>
      )}
      */}
    </BlockStack>
  );
};

export default ClaimedPromotions;
