import {
  Banner,
  BlockStack,
  Text,
} from "@shopify/ui-extensions-react/checkout";
import CallForActionMessage from "./CallForActionMessage";
import {
  BXGYConfig,
  PromoConfig,
  PromotionLines,
} from "../../../types/promotion";

type Props = {
  buyXgetYLine: PromotionLines["bxgy"];
  promoConfig: PromoConfig;
};

const UnclaimedBXGYBanner = ({ buyXgetYLine, promoConfig }: Props) => {
  const config = promoConfig as BXGYConfig;

  if (!promoConfig.enabled || buyXgetYLine.y) return null;

  return (
    <Banner title="Buy One Get One Free">
      <BlockStack spacing="tight">
        <Text>
          {buyXgetYLine.x ? (
            config.pendingMessage
          ) : (
            <CallForActionMessage productPageUrl={config.product_page_url} />
          )}
        </Text>
      </BlockStack>
    </Banner>
  );
};

export default UnclaimedBXGYBanner;
