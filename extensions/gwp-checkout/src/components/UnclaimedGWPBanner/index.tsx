import { useMemo } from "react";
import {
  Banner,
  BlockStack,
  Text,
  useApi,
  useCartLines,
  useSubtotalAmount,
  useTotalAmount,
} from "@shopify/ui-extensions-react/checkout";
import SpendMoreMessage from "./SpendMoreMessage";

import {
  formatLocalCurrency,
  type CurrencyConversion,
} from "../../../helpers/currency";
import { calculateCartTotal } from "../../../helpers/cart";
import {
  PromoConfig,
  GWPConfig,
  PromotionLines,
} from "../../../types/promotion";

type Props = {
  promoConfig: PromoConfig;
  currencyConversion: CurrencyConversion;
  gwpLine: PromotionLines["gwp"];
};

const UnclaimedGWPBanner = ({
  promoConfig,
  currencyConversion,
  gwpLine,
}: Props) => {
  const { i18n } = useApi();
  const cartLines = useCartLines();
  const subtotalAmount = useSubtotalAmount();
  const totalAmount = useTotalAmount();
  const { enabled, label, bannerTitle, threshold, pendingMessage, product_page_url } =
    promoConfig as GWPConfig;

  const cartTotal = calculateCartTotal({
    cartLines,
    currencyConversion,
  });

  const formattedSpending = useMemo(() => {
    return formatLocalCurrency({
      amount: threshold - cartTotal,
      currencyCode: subtotalAmount.currencyCode,
      currencyConversion: currencyConversion,
      i18n: i18n,
    });
  }, [cartTotal, threshold, subtotalAmount.currencyCode, currencyConversion]);

  if (!enabled || !cartTotal || gwpLine) return null;

  return (
    <BlockStack spacing="loose">
      <Banner title={label || bannerTitle}>
        {cartTotal >= threshold ? (
          <Text>{pendingMessage}</Text>
        ) : (
          <SpendMoreMessage
            formattedSpending={formattedSpending}
            product_page_url={product_page_url}
          />
        )}
      </Banner>
    </BlockStack>
  );
};

export default UnclaimedGWPBanner;
