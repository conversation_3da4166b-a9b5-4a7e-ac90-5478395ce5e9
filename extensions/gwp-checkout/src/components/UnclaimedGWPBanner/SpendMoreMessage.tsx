import { BlockStack, <PERSON>, Text } from "@shopify/ui-extensions-react/checkout";

type Props = {
  formattedSpending: string;
  product_page_url: string;
};

const SpendMoreMessage = ({
  formattedSpending,
  product_page_url,
}: Props) => {
  return (
    <BlockStack spacing="tight">
      <Text>Spend {formattedSpending} more to receive your free gift!</Text>

      {product_page_url && (
        <Link to={product_page_url} appearance="monochrome" external>
          Explore Now
        </Link>
      )}
    </BlockStack>
  );
};

export default SpendMoreMessage;
