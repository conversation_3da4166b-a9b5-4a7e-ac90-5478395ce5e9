import { useMemo } from "react";
import { Text, BlockStack, View } from "@shopify/ui-extensions-react/checkout";
import { CartLine } from "@shopify/ui-extensions/checkout";
import { findPromotionLine } from "../../helpers/promotions";
import { PromoConfig, PromotionLines } from "../../types/promotion";
import { CurrencyConversion } from "../../helpers/currency";
import UnclaimedGWPBanner from "./UnclaimedGWPBanner";
import UnclaimedBXGYBanner from "./UnclaimedBXGYBanner";

type Props = {
  cartLines: CartLine[];
  promoConfigs: {
    [key: string]: PromoConfig;
  };
  currencyConversion: CurrencyConversion;
};

const UnclaimedPromotions = ({
  cartLines,
  promoConfigs,
  currencyConversion,
}: Props) => {
  const { gwp1, gwp2, bxgy } = promoConfigs;

  const promotionLines = useMemo(() => {
    return {
      gwp1: findPromotionLine(cartLines, promoConfigs.gwp1) as PromotionLines["gwp"],
      gwp2: findPromotionLine(cartLines, promoConfigs.gwp2) as PromotionLines["gwp"],
      bxgy: findPromotionLine(cartLines, promoConfigs.bxgy) as PromotionLines["bxgy"],
    };
  }, [cartLines, promoConfigs]);

  const hasEnabledPromos = Object.values(promoConfigs).some(config => config.enabled);
  const hasUnclaimedPromos = Object.entries(promoConfigs).some(
    ([key, config]) => {
      if (!config.enabled) return false;
      if (config.type === 'bxgy') {
        const bxgyLine = promotionLines[key as keyof typeof promotionLines] as PromotionLines["bxgy"];
        return !bxgyLine?.y;
      }
      return !promotionLines[key as keyof typeof promotionLines];
    }
  );

  if (!hasEnabledPromos || !hasUnclaimedPromos) return null;

  return (
    <View>
      <BlockStack padding={["base", "none", "none", "none"]}>
        <BlockStack spacing="base">
          <Text size="medium" emphasis="bold">
            Don't Miss Out
          </Text>

          <BlockStack spacing="base">
            {/* GWP1 */}
            <UnclaimedGWPBanner
              promoConfig={gwp1}
              gwpLine={promotionLines.gwp1}
              currencyConversion={currencyConversion}
            />

            {/* GWP2 */}
            <UnclaimedGWPBanner
              promoConfig={gwp2}
              gwpLine={promotionLines.gwp2}
              currencyConversion={currencyConversion}
            />

            {/* BXGY-DISABLED: BXGY component temporarily disabled but preserved for future use */}
            {/* 
            <UnclaimedBXGYBanner
              promoConfig={bxgy}
              buyXgetYLine={promotionLines.bxgy}
            />
            */}
          </BlockStack>
        </BlockStack>
      </BlockStack>
    </View>
  );
};

export default UnclaimedPromotions;
