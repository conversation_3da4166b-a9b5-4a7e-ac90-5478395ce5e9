import { I18n } from "@shopify/ui-extensions/checkout";

type CurrencyConversion = {
  amount: string;
  currency_code: string;
} | null;

const getRate = (amount: string) => {
  return parseFloat(amount) / 100000;
};

const convertToJPY = ({
  amount,
  currencyConversion,
}: {
  amount: number;
  currencyConversion: CurrencyConversion;
}) => {
  if (!currencyConversion) {
    return amount;
  }

  const rate = getRate(currencyConversion.amount);
  return amount / rate;
};

// Format currency for display
const formatLocalCurrency = ({
  amount,
  currencyCode,
  currencyConversion,
  i18n,
}: {
  amount: number;
  currencyCode: string;
  currencyConversion: CurrencyConversion;
  i18n: I18n;
}) => {
  const { formatCurrency } = i18n;

  if (!currencyConversion || currencyCode === "JPY") {
    return formatCurrency(amount, {
      currency: currencyCode,
    });
  }

  // Convert JPY to local currency by multiplying by the rate
  const rate = getRate(currencyConversion.amount);
  return formatCurrency(amount * rate, {
    currency: currencyCode,
  });
};

export { convertToJPY, type CurrencyConversion, formatLocalCurrency };
