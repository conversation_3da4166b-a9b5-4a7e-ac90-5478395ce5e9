import { CartLine, Money } from "@shopify/ui-extensions/checkout";
import { CurrencyConversion, convertToJPY } from "./currency";

const calculateCartTotal = ({
  cartLines,
  currencyConversion,
}: {
  cartLines: CartLine[];
  currencyConversion: CurrencyConversion;
}) => {
  let productTotal = 0;
  cartLines.forEach((line) => {
    productTotal += line.cost.totalAmount.amount;
  })

  console.log("GWP_CT Product Total:", productTotal);
  return convertToJPY({
    amount: productTotal,
    currencyConversion: currencyConversion,
  });
};

export { calculateCartTotal };
