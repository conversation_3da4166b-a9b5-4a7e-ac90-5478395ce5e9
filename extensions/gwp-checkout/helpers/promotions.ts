import {
  CartLine,
  CartLineChange,
  CartLineChangeResult,
} from "@shopify/ui-extensions/checkout";
import { PromoConfig, PromoSettings, PromotionLines } from "../types/promotion";

/**
 * Creates a configuration object for promotions based on the Checkout Extension settings.
 * @param settings - The Checkout Extension settings.
 * @returns An object containing configurations for different types of promotions.
 */
const createPromoConfig = (settings: PromoSettings) => ({
  gwp1: {
    id: "gwp1",
    type: "gwp" as const,
    enabled: settings.enable_free_sample_product,
    productId: settings.free_product_id,
    threshold: parseFloat(settings.free_prod_limit),
    label: settings.free_prod_tag_1,
    inventoryLimit: parseInt(settings.free_prod_inventory_limit),
    product_page_url: settings.gwp1_url,
    bannerTitle: "Free Gift Offer",
    successMessage: "Free gift 1 added to your cart!",
    pendingMessage: "Adding free gift 1 to your cart...",
  },
  gwp2: {
    id: "gwp2",
    type: "gwp" as const,
    enabled: settings.enable_free_sample_product_2,
    productId: settings.free_product_id_2,
    threshold: parseFloat(settings.free_prod_limit_2),
    label: settings.free_prod_tag_2,
    inventoryLimit: parseInt(settings.free_prod_inventory_limit_2),
    product_page_url: settings.gwp2_url,
    bannerTitle: "Premium Free Gift Offer",
    successMessage: "Free gift 2 added to your cart!",
    pendingMessage: "Adding 2nd free gift to your cart...",
  },
  bxgy: {
    id: "bxgy",
    type: "bxgy" as const,
    // BXGY-DISABLED: BXGY is temporarily disabled, but code is preserved for future use
    enabled: false, // Force disabled regardless of settings
    productXId: settings.product_x_id || "",
    productXExcludedVariantIds: settings.product_x_excluded_variant_ids || "",
    productYId: settings.product_y_id || "",
    product_page_url: settings.bxgy_url || "",
    bannerTitle: "Buy One Get One Free",
    successMessage: "Your free product has been added!",
    pendingMessage: "Adding your free product...",
  },
});

const findPromotionLine = (
  cartLines: CartLine[],
  config: PromoConfig
): PromotionLines[keyof PromotionLines] => {
  switch (config.type) {
    case "gwp":
      const gwpLine = cartLines.find(
        (line) =>
          line.merchandise.id ===
          `gid://shopify/ProductVariant/${config.productId}`
      );
      return gwpLine || null;

    case "bxgy":
      const excludedVariantIds = config.productXExcludedVariantIds
        ? config.productXExcludedVariantIds.split(",").map((id) => id.trim())
        : [];
      const productXLines = cartLines.filter(
        (line) =>
          line.merchandise.product.id === `gid://shopify/Product/${config.productXId}` &&
          !excludedVariantIds.includes(line.merchandise.id.split("/").pop() || "")
      );

      return {
        x: productXLines[0] || null,
        y:
          cartLines.find(
            (line) =>
              line.merchandise.id ===
                `gid://shopify/ProductVariant/${config.productYId}` ||
              line.attributes.some(
                (attr) => attr.key === "BXGY" && attr.value === "Free Product Y"
              )
          ) || null,
      };

    default:
      throw new Error("Unhandled promotion type");
  }
};

const handlePromotion = async ({
  config,
  cartTotal,
  promotionLine,
  applyCartLinesChange,
  promoState,
  setPromoState,
}: {
  config: PromoConfig;
  cartTotal: number;
  promotionLine: PromotionLines[keyof PromotionLines];
  promoState: { [key: string]: boolean };
  applyCartLinesChange: (
    changes: CartLineChange
  ) => Promise<CartLineChangeResult>;
  setPromoState: (state: { [key: string]: boolean }) => void;
}) => {
  const stateKey = `${config.id}Added`;
  console.log("Processing:", {
    cartTotal,
    promotionLine,
  });

  switch (config.type) {
    case "gwp":
      // If promotion line exists but shouldn't
      if (promotionLine && (!config.enabled || cartTotal < config.threshold)) {
        console.log("Removing GWP");

        await applyCartLinesChange({
          type: "removeCartLine",
          id: (promotionLine as CartLine).id,
          quantity: (promotionLine as CartLine).quantity,
        });
        setPromoState({ ...promoState, [stateKey]: false });
        return;
      }

      // If promotion line should exist but doesn't
      if (!promotionLine && config.enabled && cartTotal >= config.threshold) {
        console.log("Adding GWP");

        await applyCartLinesChange({
          type: "addCartLine",
          merchandiseId: `gid://shopify/ProductVariant/${config.productId}`,
          quantity: 1,
        });
        setPromoState({ ...promoState, [stateKey]: true });
        return;
      }

      // If promotion line should exist but quantity is greater than 1
      if (
        promotionLine &&
        config.enabled &&
        cartTotal >= config.threshold &&
        (promotionLine as CartLine).quantity > 1
      ) {
        console.log("Fixing GWP quantity");

        await applyCartLinesChange({
          type: "updateCartLine",
          id: (promotionLine as CartLine).id,
          quantity: 1,
        });
        return;
      }

      console.log("GWP status maintained");
      break;

    case "bxgy":
      const bxgyLines = promotionLine as PromotionLines["bxgy"];

      // If Y exists but shouldn't
      if (bxgyLines.y && (!config.enabled || !bxgyLines.x)) {
        console.log("Removing BXGY");

        await applyCartLinesChange({
          type: "removeCartLine",
          id: bxgyLines.y.id,
          quantity: bxgyLines.y.quantity,
        });
        setPromoState({ ...promoState, [stateKey]: false });
        return;
      }

      // If Y should exist but doesn't
      if (bxgyLines.x && !bxgyLines.y && config.enabled) {
        console.log("Adding BXGY");

        await applyCartLinesChange({
          type: "addCartLine",
          merchandiseId: `gid://shopify/ProductVariant/${config.productYId}`,
          quantity: 1,
        });
        setPromoState({ ...promoState, [stateKey]: true });
        return;
      }

      // If Y exists but quantity is greater than 1
      if (
        bxgyLines.x &&
        bxgyLines.y &&
        config.enabled &&
        bxgyLines.y.quantity > 1
      ) {
        console.log("Fixing BXGY quantity");

        await applyCartLinesChange({
          type: "updateCartLine",
          id: bxgyLines.y.id,
          quantity: 1,
        });
        setPromoState({ ...promoState, [stateKey]: true });
        return;
      }

      console.log("BXGY status maintained");
      break;

    default:
      throw new Error("Unhandled promotion type");
  }
};

export { createPromoConfig, findPromotionLine, handlePromotion };
