import {
  reactExtension,
  Banner,
  BlockStack,
  Checkbox,
  Text,
  useApi,
  useApplyAttributeChange,
  useInstructions,
  useTranslate,
  InlineStack,
  ChoiceList,
  Choice,
  Icon,
  useCartLines,
  useApplyCartLinesChange,
  useAttributes,
  Heading,
  useSettings,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useState } from "react";

// 1. Choose an extension target
export default reactExtension("purchase.checkout.block.render", () => (
  <Extension />
));

function Extension() {
  const translate = useTranslate();
  const { extension } = useApi();
  const instructions = useInstructions();
  const attributes = useAttributes();
  const applyAttributeChange = useApplyAttributeChange();
  const cartLines = useCartLines();
  const applyCartLinesChange = useApplyCartLinesChange();
  const [selectedOption, setSelectedOption] = useState("");
  const {
    signature_service_variant_id: signatureServiceVariantId,
    signature_service_product_id: signatureServiceProductId,
    signature_service_title: signatureServiceTitle,
    signature_service_description: signatureServiceDescription,
    no_signature_service_option_label: noSignatureServiceOptionLabel,
    signature_service_option_label: signatureServiceOptionLabel,
  } = useSettings();

  const addSignatureService = async () => {
    setSelectedOption("signature");
    try {
      await applyCartLinesChange({
        type: "addCartLine",
        merchandiseId: `gid://shopify/ProductVariant/${signatureServiceVariantId}`,
        quantity: 1,
      });
      await applyAttributeChange({
        key: "signatureService",
        type: "updateAttribute",
        value: "true",
      });
    } catch (error) {
      console.error("Error adding signature service", error);
    }
  };

  const removeSignatureService = async () => {
    setSelectedOption("no-signature");
    const line = cartLines.filter(
      (line) =>
        line.merchandise.product.id.split("gid://shopify/Product/").pop() ===
        signatureServiceProductId
    )[0];
    if (line) {
      try {
        await applyCartLinesChange({
          type: "removeCartLine",
          id: line.id,
          quantity: 1,
        });
        if (attributes.some((a) => a.key === "signatureService")) {
          await applyAttributeChange({
            key: "signatureService",
            type: "removeAttribute",
          });
        }
      } catch (error) {
        console.error("Error removing signature service", error);
      }
    }
  };

  const handleChange = (value: string) => {
    if (value === "signature") {
      addSignatureService();
    } else {
      removeSignatureService();
    }
  };

  useEffect(() => {
    if (
      cartLines.some(
        (line) =>
          line.merchandise.product.id.split("gid://shopify/Product/").pop() ===
          signatureServiceProductId
      )
    ) {
      setSelectedOption("signature");
      const signatureServiceLine = cartLines.filter(
        (line) =>
          line.merchandise.product.id.split("gid://shopify/Product/").pop() ===
          signatureServiceProductId
      )[0];
      if (signatureServiceLine && signatureServiceLine.quantity > 1) {
        const removeQuantity = signatureServiceLine.quantity - 1;
        applyCartLinesChange({
          type: "removeCartLine",
          id: signatureServiceLine.id,
          quantity: removeQuantity,
        });
      }
    } else {
      setSelectedOption("no-signature");
    }
  }, []);

  // 2. Check instructions for feature availability, see https://shopify.dev/docs/api/checkout-ui-extensions/apis/cart-instructions for details
  if (!instructions.attributes.canUpdateAttributes) {
    // For checkouts such as draft order invoices, cart attributes may not be allowed
    // Consider rendering a fallback UI or nothing at all, if the feature is unavailable
    return (
      <Banner title="checkbox-radio-button-add-product-block" status="warning">
        {translate("attributeChangesAreNotSupported")}
      </Banner>
    );
  }

  // 3. Render a UI
  return (
    <BlockStack>
      {
        signatureServiceTitle && (
          <Heading level={1}>{signatureServiceTitle}</Heading>
        )
      }
      {
        signatureServiceDescription && (
          <Text appearance="subdued">
            {signatureServiceDescription}
          </Text>
        )
      }
      <ChoiceList
        name="group-single"
        variant="group"
        value={selectedOption}
        onChange={(value) => {
          handleChange(value as string);
        }}
      >
        <Choice id="no-signature">{noSignatureServiceOptionLabel}</Choice>
        <Choice id="signature">{signatureServiceOptionLabel}</Choice>
      </ChoiceList>
    </BlockStack>
  );
}
