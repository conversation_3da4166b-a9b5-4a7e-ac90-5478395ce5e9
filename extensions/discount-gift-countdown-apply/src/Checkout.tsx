/**
 * This extension is for showing a color box under shopify native discount widget
 * The box includes a countdown timer and onclick to apply the discount code or gift card code
 * Data gets from the backend api
 * If customer is logged in, Extension will automatically remove all the discount codes and gift card codes when first enter the page, 
 * and then automatically apply the new code fetched from the backend api
 */
import {
  reactExtension,
  BlockStack,
  Text,
  useTranslate,
  InlineLayout,
  View,
  useDiscountCodes,
  useAppliedGiftCards,
  Pressable,
  useApplyDiscountCodeChange,
  useApplyGiftCardChange,
  Icon,
  useCartLines,
  useSettings,
  useCustomer,
  useSessionToken,
  useApi,
} from "@shopify/ui-extensions-react/checkout";
import { useEffect, useRef, useState } from "react";

// 1. Choose an extension target
export default reactExtension("purchase.checkout.reductions.render-after", () => (
  <Extension />
));

function Extension() {
  const {i18n} = useApi();
  const translate = useTranslate();
  const applyDiscountCodeChange = useApplyDiscountCodeChange();
  const applyGiftCardChange = useApplyGiftCardChange();
  const cartLineItems = useCartLines();
  const { get: getSessionToken } = useSessionToken();
  const customer = useCustomer();
  const { api_domain: apiUrl, api_x_appid: appId } = useSettings<{
    api_domain: string;
    api_x_appid: string;
  }>();
  const couponCode = useDiscountCodes();
  const giftCardCode = useAppliedGiftCards();
  const [coupon, setCoupon] = useState<any>(null);
  const [countdown, setCountdown] = useState("00 : 00 : 00 : 00");
  const isCouponApplied = useRef(false);

  const createTraceId = () => {
    const date = new Date();
    const year = `00${date?.getFullYear()}`.slice(-2);
    const month = `00${date.getMonth() + 1}`.slice(-2);
    const day = `00${date.getDay()}`.slice(-2);
    const hour = `00${date.getHours()}`.slice(-2);
    const minute = `00${date.getMinutes()}`.slice(-2);
    const second = `00${date.getSeconds()}`.slice(-2);
    const millSecond = `000${date.getMilliseconds()}`.slice(-3);
    const randomStr1 = `${Math.random()}`;
    const randomStr2 = `${Math.random()}`;
    return (
      year +
      month +
      day +
      hour +
      minute +
      second +
      millSecond +
      randomStr1.slice(-8) +
      randomStr2.slice(-9)
    );
  };

  // Fetch the coupon code and apply the code
  const fetchCouponsAndApplyCode = async () => {
    console.log("fetchCoupons called");
    const productList = cartLineItems.map((item) => ({
      productId: item.merchandise.product.id.split("/").pop(),
      variantId: item.merchandise.id.split("/").pop(),
      quantity: item.quantity,
      price: item.cost.totalAmount.amount / item.quantity,
    }));
    try {
      const sessionToken = await getSessionToken();
      console.log("fetch coupon here");
      const response = await fetch(
        `${apiUrl}/integral/web/rewardRedeemCoupon/cart/coupon/listForShopify`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-appid": appId,
            traceId: createTraceId(),
            "shopify-token": `Bearer ${sessionToken}`,
            "Accept-Language": "en-US,en;q=0.9",
          },
          body: JSON.stringify({
            productList,
            source: "CHECK_OUT",
            customerId: customer?.id.split("/").pop(),
          }),
        }
      );
      const data = await response.json();
      if (data.message === "Success") {
        if (data.data.coupons.length === 0) {
          setCoupon(null);
          return;
        }
        setCoupon(data.data.coupons[0]);
        if (!isCouponApplied.current) {
          // If gift card code is applied, remove the gift card code and apply the new gift card code
          const isGiftCardCodeApplied = giftCardCode.length > 0;
          if (isGiftCardCodeApplied && data.data.coupons[0]?.ruleType === "GIFT_CARD") {
            await removeCodes("GIFT_CARD", giftCardCode);
            onCodePress(
              data.data.coupons[0]?.code,
              data.data.coupons[0]?.ruleType
            );
          } else if (!isGiftCardCodeApplied && data.data.coupons[0]?.ruleType === "GIFT_CARD") {
            onCodePress(
              data.data.coupons[0]?.code,
              data.data.coupons[0]?.ruleType
            );
          } else if (data.data.coupons[0]?.ruleType === "COUPONS") {
            // If coupon code is applied, do not remove the coupon code and directly apply the new coupon code, shopify will automatically take the larger discount
            onCodePress(
              data.data.coupons[0]?.code,
              data.data.coupons[0]?.ruleType
            );
          }
          isCouponApplied.current = true;
        }
      }
    } catch (error) {
      console.log("fetchCoupons error", error);
    } finally {
      // Set the isCouponApplied to true to indicate that the code is applied
      isCouponApplied.current = true;
    }
  };


  useEffect(() => {
    // Call api to get discount code and gift card code
    // If customer is logged in, remove all the discount codes and gift card codes when first enter the page, 
    // and then automatically apply the new code fetched from the backend api
    if (cartLineItems.length > 0 && customer && customer.id) {
      // const isAnyCodeApplied = couponCode.length > 0 || giftCardCode.length > 0;
      // If any code is applied and api is not yet called, remove the code, then fetch the new code and apply it
      if (!isCouponApplied.current) {
        fetchCouponsAndApplyCode();
      }
      // // If no code is applied and api is not yet called, fetch the code and apply it
      // if (!isAnyCodeApplied && !isCouponApplied.current) {
      //   fetchCouponsAndApplyCode();
      // }
    }
  }, []);

  // Update the countdown timer
  useEffect(() => {
    const updateCountdown = () => {
      // Fix timezone to be Asia/Shanghai time
      // Transform the endDate to the format of "2025/01/20 10:52:09 GMT+8" to avoid compatibility issue for ios
      const modifiedEndDate = coupon?.endTime.replaceAll("-", "/") + " GMT+8";
      const beijingEndDateToStoreTimezoneEndDate = new Date(modifiedEndDate).toLocaleString("en-US", {timeZone: "Asia/Shanghai"});
      const storeTimezoneEndDateInUnix = new Date(beijingEndDateToStoreTimezoneEndDate).getTime();
      const storeTimezoneCurrentDate = new Date().toLocaleString("en-US", {timeZone: "Asia/Shanghai"});
      const storeTimezoneCurrentDateInUnix = new Date(storeTimezoneCurrentDate).getTime();
      const distance = storeTimezoneEndDateInUnix - storeTimezoneCurrentDateInUnix;

      if (distance < 0) {
        setCountdown("00 : 00 : 00 : 00");
        return;
      }

      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      setCountdown(
        `${days.toString().padStart(2, "0")} : ${hours
          .toString()
          .padStart(2, "0")} : ${minutes
          .toString()
          .padStart(2, "0")} : ${seconds.toString().padStart(2, "0")}`
      );
    };

    // Run immediately
    updateCountdown();

    // Set up interval and store reference
    const intervalId = setInterval(updateCountdown, 1000);

    // Cleanup interval on component unmount
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [coupon]); // Empty dependency array means this effect runs once on mount

  const couponBarColor = coupon?.ruleType === "GIFT_CARD" ? "accent" : "warning";
  const couponBarIcon = coupon?.ruleType === "GIFT_CARD" ? "gift" : "discount";

  if (!coupon) {
    return null;
  };

  // Hide widget if the coupon is fetched, but the current code is not same as the coupon code
  if (isCouponApplied.current) {
    if (coupon?.ruleType === "COUPONS" && !couponCode.some(code => code.code === coupon?.code)) {
      return null;
    }
    // Hide widget if the gift card is fetched, but the current code is not same as the coupon code
    if (coupon?.ruleType === "GIFT_CARD" && !giftCardCode.some(code => code.lastCharacters.toUpperCase() === coupon?.code.slice(-4).toUpperCase())) {
      return null;
    }
    // Hide widget if the code is fetched, but no code is applied
    if (couponCode.length === 0 && giftCardCode.length === 0) {
      return null;
    }
  }

  // 3. Render a UI
  return (
    <BlockStack>
      <Pressable
        display="block"
        background="subdued"
        padding="none"
        borderRadius="base"
        onPress={() => onCodePress(coupon.code, coupon.ruleType)}
      >
        <InlineLayout columns={["fill", "auto"]}>
          <InlineLayout
            columns={[18, "fill"]}
            padding={["extraTight", "base", "extraTight", "base"]}
          >
            <Icon source={couponBarIcon} appearance={couponBarColor} />
            <View padding={["none", "none", "none", "extraTight"]}>
              <Text appearance={couponBarColor}>
                {coupon?.ruleType === "GIFT_CARD" ? (
                  <>
                    {"•••• " + coupon?.code?.slice(-4).toUpperCase()} ({translate("giftCard")})
                  </>
                ) : (
                  <>{coupon?.code}</>
                )}
              </Text>
            </View>
          </InlineLayout>
          <InlineLayout inlineAlignment="end">
            <View padding={["extraTight", "base", "extraTight", "base"]}>
              <Text appearance={couponBarColor}>
                -{i18n.formatCurrency(Number(coupon?.discountAmount))}
              </Text>
            </View>
          </InlineLayout>
        </InlineLayout>

        <View padding={["none", "base", "extraTight", "base"]}>
          <Text appearance={couponBarColor}>
            {translate("expiresOn")} <Text>{countdown}</Text>
          </Text>
        </View>
      </Pressable>
    </BlockStack>
  );

  async function removeCodes(type, codes) {
    if (type === "COUPONS") {
      codes.forEach(async code => {
        await applyDiscountCodeChange({
          code: code.code,
          type: "removeDiscountCode",
        });
      });
    }
    if (type === "GIFT_CARD") {
      codes.forEach(async code => {
        await applyGiftCardChange({
          code: code.lastCharacters,
          type: "removeGiftCard",
        });
      });
    }
  }

  // Apply the discount code or gift card code
  async function onCodePress(code, ruleType) {
    if (ruleType === "GIFT_CARD") {
      try {
        const result = await applyGiftCardChange({
          code: code,
          type: "addGiftCard",
        });
      } catch (error) {
        console.log("applyGiftCardChange error", error);
      }
    } else {
      try {
        const result = await applyDiscountCodeChange({
          code: code,
          type: "addDiscountCode",
        });
      } catch (error) {
        console.log("applyDiscountCodeChange error", error);
      }
    }
  }
}
