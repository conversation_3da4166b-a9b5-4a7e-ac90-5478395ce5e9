# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "2c5d8d170e970ed740c856902ee8c3f2"
name = "Ecoflow Checkout Widgets Dev"
handle = "ecoflow-checkout-widgets-dev"
application_url = "https://shopify.dev/apps/default-app-home"
embedded = true

[build]
dev_store_url = "ecoflow-loyalty-app-dev-kyle.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_checkouts,read_customers,read_discounts,read_products,unauthenticated_read_checkouts,unauthenticated_read_customers"

[auth]
redirect_urls = [ "https://shopify.dev/apps/default-app-home/api/auth" ]

[webhooks]
api_version = "2024-04"

[pos]
embedded = false
